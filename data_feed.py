"""
Enhanced Data Feed with real API integration
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict
import yfinance as yf
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient

logger = logging.getLogger(__name__)

class DataFeed:
    """
    Unified data feed that combines multiple data sources
    """

    def __init__(self):
        """Initialize data feed with API clients"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            self.yf_fallback = True  # Use yfinance as fallback
            logger.info("DataFeed initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing DataFeed: {e}")
            self.alpaca_client = None
            self.fmp_client = None
            self.yf_fallback = True

    def get_stock_data(self, symbol: str, timeframe: str = '5m',
                      period: int = 100, use_fallback: bool = True) -> pd.DataFrame:
        """
        Get stock data from primary source (Alpaca) with fallback options
        """
        try:
            # Try Alpaca first
            if self.alpaca_client:
                df = self._get_alpaca_data(symbol, timeframe, period)
                if not df.empty:
                    return df
                logger.warning(f"No data from Alpaca for {symbol}, trying fallback")

            # Fallback to yfinance
            if use_fallback and self.yf_fallback:
                df = self._get_yfinance_data(symbol, timeframe, period)
                if not df.empty:
                    return df
                logger.warning(f"No data from yfinance for {symbol}")

            # Last resort: simulated data for testing
            logger.warning(f"Using simulated data for {symbol}")
            return self._get_simulated_data(symbol, period)

        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return self._get_simulated_data(symbol, period)

    def _get_alpaca_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from Alpaca API"""
        try:
            # Calculate start date based on period and timeframe
            if timeframe == '1m':
                start_date = datetime.now() - timedelta(minutes=period)
            elif timeframe == '5m':
                start_date = datetime.now() - timedelta(minutes=period * 5)
            elif timeframe == '15m':
                start_date = datetime.now() - timedelta(minutes=period * 15)
            elif timeframe == '1h':
                start_date = datetime.now() - timedelta(hours=period)
            else:  # 1d
                start_date = datetime.now() - timedelta(days=period)

            df = self.alpaca_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                start=start_date.strftime('%Y-%m-%d'),
                limit=period
            )

            return df

        except Exception as e:
            logger.error(f"Error getting Alpaca data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_yfinance_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from yfinance as fallback"""
        try:
            # Map timeframe to yfinance intervals
            interval_map = {
                '1m': '1m', '5m': '5m', '15m': '15m',
                '1h': '1h', '1d': '1d'
            }
            interval = interval_map.get(timeframe, '5m')

            # Map period to yfinance period
            if timeframe in ['1m', '5m']:
                yf_period = '7d'  # Max for minute data
            elif timeframe == '15m':
                yf_period = '60d'
            elif timeframe == '1h':
                yf_period = '730d'
            else:
                yf_period = '2y'

            ticker = yf.Ticker(symbol)
            df = ticker.history(period=yf_period, interval=interval)

            if df.empty:
                return pd.DataFrame()

            # Standardize column names
            df.columns = df.columns.str.lower()
            df = df[['open', 'high', 'low', 'close', 'volume']].tail(period)

            return df

        except Exception as e:
            logger.error(f"Error getting yfinance data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_simulated_data(self, symbol: str, period: int) -> pd.DataFrame:
        """Generate simulated data for testing (original function)"""
        np.random.seed(hash(symbol) % 2**32)
        dates = pd.date_range(end=datetime.now(), periods=period, freq='5min')
        price = np.cumsum(np.random.randn(period)) + 100
        high = price + np.random.rand(period)
        low = price - np.random.rand(period)
        open_ = price + np.random.randn(period) * 0.5
        close = price
        volume = np.random.randint(1000, 10000, size=period)

        return pd.DataFrame({
            'open': open_,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=dates)

    def get_fundamental_data(self, symbol: str) -> Optional[Dict]:
        """Get fundamental data for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_company_profile(symbol)
        return None

    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_financial_ratios(symbol)
        return None

    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        if self.alpaca_client:
            return self.alpaca_client.get_latest_quote(symbol)
        return None

    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_client:
            return self.alpaca_client.is_market_open()

        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)

        return market_open <= now <= market_close

# Backward compatibility function
def get_stock_data(symbol: str, timeframe: str = '5m', period: int = 100) -> pd.DataFrame:
    """Backward compatible function for existing code"""
    data_feed = DataFeed()
    return data_feed.get_stock_data(symbol, timeframe, period)
