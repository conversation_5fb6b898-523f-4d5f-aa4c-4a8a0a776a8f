import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def get_stock_data(symbol):
    # Placeholder: simulate stock data
    np.random.seed(hash(symbol) % 2**32)
    dates = pd.date_range(end=datetime.now(), periods=100, freq='5min')
    price = np.cumsum(np.random.randn(100)) + 100
    high = price + np.random.rand(100)
    low = price - np.random.rand(100)
    open_ = price + np.random.randn(100) * 0.5
    close = price
    volume = np.random.randint(1000, 10000, size=100)

    return pd.DataFrame({
        'open': open_,
        'high': high,
        'low': low,
        'close': close,
        'volume': volume
    }, index=dates)
