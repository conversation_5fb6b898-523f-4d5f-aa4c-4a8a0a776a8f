"""
Enhanced Data Feed with real API integration
"""
import pandas as pd
from datetime import datetime
import logging
from typing import Optional, Dict
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient

logger = logging.getLogger(__name__)

class DataFeed:
    """
    Unified data feed that combines multiple data sources
    """

    def __init__(self):
        """Initialize data feed with Alpaca and FMP API clients ONLY"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            logger.info("DataFeed initialized successfully - Alpaca + FMP APIs only")
        except Exception as e:
            logger.error(f"Error initializing DataFeed: {e}")
            self.alpaca_client = None
            self.fmp_client = None

    def get_stock_data(self, symbol: str, timeframe: str = '5m',
                      period: int = 100, use_fallback: bool = True) -> pd.DataFrame:
        """
        Get stock data from Alpaca and FMP APIs ONLY - NO YFINANCE, NO SIMULATED DATA
        """
        try:
            # Primary: Alpaca API for market data
            if self.alpaca_client:
                df = self._get_alpaca_data(symbol, timeframe, period)
                if not df.empty:
                    logger.info(f"Retrieved {len(df)} bars from Alpaca for {symbol}")
                    return df
                logger.warning(f"No data from Alpaca for {symbol}")

            # Secondary: Try FMP for daily data if Alpaca fails or insufficient data
            if use_fallback and self.fmp_client:
                df_fmp = self._get_fmp_data(symbol, period)
                if not df_fmp.empty and len(df_fmp) > len(df) if not df.empty else True:
                    logger.info(f"Retrieved {len(df_fmp)} bars from FMP for {symbol}")
                    return df_fmp
                logger.warning(f"No additional data from FMP for {symbol}")

            # NO OTHER FALLBACKS - Return empty DataFrame
            logger.error(f"No real market data available for {symbol} from Alpaca/FMP APIs")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_alpaca_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from Alpaca API - optimized for real market data"""
        try:
            # Request more data than needed to ensure we get enough bars
            # Don't use start date restriction - just get the most recent data available
            requested_limit = max(period * 3, 100)  # Get at least 100 bars or 3x requested

            df = self.alpaca_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=requested_limit
            )

            if df.empty:
                logger.debug(f"No historical data available for {symbol}")
                return pd.DataFrame()

            # Return the requested number of periods (most recent)
            if len(df) > period:
                df = df.tail(period)

            logger.debug(f"Retrieved {len(df)} bars for {symbol} (requested {period})")
            return df

        except Exception as e:
            logger.error(f"Error getting Alpaca data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_fmp_data(self, symbol: str, period: int) -> pd.DataFrame:
        """Get historical data from FMP API as fallback"""
        try:
            if not self.fmp_client:
                return pd.DataFrame()

            # FMP provides historical daily data
            # Get more data than requested to ensure we have enough
            requested_period = max(period * 2, 100)

            # Use FMP historical price endpoint
            historical_data = self.fmp_client.get_historical_prices(symbol, limit=requested_period)

            if not historical_data:
                logger.debug(f"No historical data from FMP for {symbol}")
                return pd.DataFrame()

            # Convert to DataFrame
            df_data = []
            for bar in historical_data:
                df_data.append({
                    'open': float(bar.get('open', 0)),
                    'high': float(bar.get('high', 0)),
                    'low': float(bar.get('low', 0)),
                    'close': float(bar.get('close', 0)),
                    'volume': int(bar.get('volume', 0))
                })

            if not df_data:
                return pd.DataFrame()

            # Create DataFrame with date index
            df = pd.DataFrame(df_data)

            # Create date index from the historical data
            dates = [pd.to_datetime(bar.get('date')) for bar in historical_data]
            df.index = pd.DatetimeIndex(dates)

            # Sort by date (oldest first) and return requested period
            df = df.sort_index()
            if len(df) > period:
                df = df.tail(period)

            logger.debug(f"Retrieved {len(df)} bars from FMP for {symbol}")
            return df

        except Exception as e:
            logger.error(f"Error getting FMP data for {symbol}: {e}")
            return pd.DataFrame()



    def get_fundamental_data(self, symbol: str) -> Optional[Dict]:
        """Get fundamental data for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_company_profile(symbol)
        return None

    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_financial_ratios(symbol)
        return None

    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        if self.alpaca_client:
            return self.alpaca_client.get_latest_quote(symbol)
        return None

    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_client:
            return self.alpaca_client.is_market_open()

        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)

        return market_open <= now <= market_close

# Backward compatibility function
def get_stock_data(symbol: str, timeframe: str = '5m', period: int = 100) -> pd.DataFrame:
    """
    Backward compatible function for existing code
    Returns REAL market data only - NO SIMULATED DATA
    """
    data_feed = DataFeed()
    return data_feed.get_stock_data(symbol, timeframe, period)
