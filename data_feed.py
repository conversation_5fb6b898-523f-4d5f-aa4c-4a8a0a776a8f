"""
Enhanced Data Feed with real API integration
"""
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient

logger = logging.getLogger(__name__)

class DataFeed:
    """
    Unified data feed that combines multiple data sources
    """

    def __init__(self):
        """Initialize data feed with Alpaca and FMP API clients ONLY"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            logger.info("DataFeed initialized successfully - Alpaca + FMP APIs only")
        except Exception as e:
            logger.error(f"Error initializing DataFeed: {e}")
            self.alpaca_client = None
            self.fmp_client = None

    def get_stock_data(self, symbol: str, timeframe: str = '5m',
                      period: int = 100, use_fallback: bool = True) -> pd.DataFrame:
        """
        Get stock data from Alpaca and FMP APIs ONLY - NO YFINANCE, NO SIMULATED DATA
        """
        try:
            # Primary: Alpaca API for market data
            if self.alpaca_client:
                df = self._get_alpaca_data(symbol, timeframe, period)
                if not df.empty:
                    logger.info(f"Retrieved {len(df)} bars from Alpaca for {symbol}")
                    return df
                logger.warning(f"No data from Alpaca for {symbol}")

            # Secondary: Try FMP for daily data if Alpaca fails
            if use_fallback and self.fmp_client and timeframe in ['1d', '1D']:
                df = self._get_fmp_data(symbol, period)
                if not df.empty:
                    logger.info(f"Retrieved {len(df)} bars from FMP for {symbol}")
                    return df
                logger.warning(f"No data from FMP for {symbol}")

            # NO OTHER FALLBACKS - Return empty DataFrame
            logger.error(f"No real market data available for {symbol} from Alpaca/FMP APIs")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_alpaca_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from Alpaca API"""
        try:
            # Calculate start date based on period and timeframe with extra buffer
            buffer_multiplier = 2  # Get extra data to ensure we have enough

            if timeframe == '1m':
                start_date = datetime.now() - timedelta(minutes=period * buffer_multiplier)
            elif timeframe == '5m':
                start_date = datetime.now() - timedelta(minutes=period * 5 * buffer_multiplier)
            elif timeframe == '15m':
                start_date = datetime.now() - timedelta(minutes=period * 15 * buffer_multiplier)
            elif timeframe == '1h':
                start_date = datetime.now() - timedelta(hours=period * buffer_multiplier)
            else:  # 1d
                start_date = datetime.now() - timedelta(days=period * buffer_multiplier)

            df = self.alpaca_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                start=start_date.strftime('%Y-%m-%d'),
                limit=period * buffer_multiplier
            )

            # Return the requested number of periods
            if not df.empty and len(df) > period:
                df = df.tail(period)

            return df

        except Exception as e:
            logger.error(f"Error getting Alpaca data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_fmp_data(self, symbol: str, period: int) -> pd.DataFrame:
        """Get daily data from FMP API as fallback for daily timeframes"""
        try:
            # FMP provides daily historical data
            if not self.fmp_client:
                return pd.DataFrame()

            # Use FMP's historical price endpoint
            # This is a simplified implementation - FMP has historical price endpoints
            # For now, return empty as we primarily use Alpaca for market data
            logger.debug(f"FMP daily data not implemented yet for {symbol} ({period} periods)")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting FMP data for {symbol}: {e}")
            return pd.DataFrame()



    def get_fundamental_data(self, symbol: str) -> Optional[Dict]:
        """Get fundamental data for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_company_profile(symbol)
        return None

    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_financial_ratios(symbol)
        return None

    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        if self.alpaca_client:
            return self.alpaca_client.get_latest_quote(symbol)
        return None

    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_client:
            return self.alpaca_client.is_market_open()

        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)

        return market_open <= now <= market_close

# Backward compatibility function
def get_stock_data(symbol: str, timeframe: str = '5m', period: int = 100) -> pd.DataFrame:
    """
    Backward compatible function for existing code
    Returns REAL market data only - NO SIMULATED DATA
    """
    data_feed = DataFeed()
    return data_feed.get_stock_data(symbol, timeframe, period)
