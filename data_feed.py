"""
Enhanced Data Feed with real API integration
"""
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Optional, Dict
from api.alpaca_client import AlpacaClient
from api.fmp_client import FMPClient

logger = logging.getLogger(__name__)

class DataFeed:
    """
    Unified data feed that combines multiple data sources
    """

    def __init__(self):
        """Initialize data feed with Alpaca and FMP API clients ONLY"""
        try:
            self.alpaca_client = AlpacaClient()
            self.fmp_client = FMPClient()
            logger.info("DataFeed initialized successfully - Alpaca + FMP APIs only")
        except Exception as e:
            logger.error(f"Error initializing DataFeed: {e}")
            self.alpaca_client = None
            self.fmp_client = None

    def get_stock_data(self, symbol: str, timeframe: str = '5m',
                      period: int = 100, use_fallback: bool = True) -> pd.DataFrame:
        """
        Get stock data from real APIs only - NO SIMULATED DATA
        """
        try:
            # Try Alpaca first
            if self.alpaca_client:
                df = self._get_alpaca_data(symbol, timeframe, period)
                if not df.empty:
                    logger.info(f"Retrieved {len(df)} bars from Alpaca for {symbol}")
                    return df
                logger.warning(f"No data from Alpaca for {symbol}, trying fallback")

            # Fallback to yfinance
            if use_fallback and self.yf_fallback:
                df = self._get_yfinance_data(symbol, timeframe, period)
                if not df.empty:
                    logger.info(f"Retrieved {len(df)} bars from yfinance for {symbol}")
                    return df
                logger.error(f"No data from yfinance for {symbol}")

            # NO SIMULATED DATA - Return empty DataFrame
            logger.error(f"No real market data available for {symbol}")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"Error getting stock data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_alpaca_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from Alpaca API"""
        try:
            # Calculate start date based on period and timeframe with extra buffer
            buffer_multiplier = 2  # Get extra data to ensure we have enough

            if timeframe == '1m':
                start_date = datetime.now() - timedelta(minutes=period * buffer_multiplier)
            elif timeframe == '5m':
                start_date = datetime.now() - timedelta(minutes=period * 5 * buffer_multiplier)
            elif timeframe == '15m':
                start_date = datetime.now() - timedelta(minutes=period * 15 * buffer_multiplier)
            elif timeframe == '1h':
                start_date = datetime.now() - timedelta(hours=period * buffer_multiplier)
            else:  # 1d
                start_date = datetime.now() - timedelta(days=period * buffer_multiplier)

            df = self.alpaca_client.get_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                start=start_date.strftime('%Y-%m-%d'),
                limit=period * buffer_multiplier
            )

            # Return the requested number of periods
            if not df.empty and len(df) > period:
                df = df.tail(period)

            return df

        except Exception as e:
            logger.error(f"Error getting Alpaca data for {symbol}: {e}")
            return pd.DataFrame()

    def _get_yfinance_data(self, symbol: str, timeframe: str, period: int) -> pd.DataFrame:
        """Get data from yfinance as fallback"""
        try:
            # Map timeframe to yfinance intervals
            interval_map = {
                '1m': '1m', '5m': '5m', '15m': '15m',
                '1h': '1h', '1d': '1d'
            }
            interval = interval_map.get(timeframe, '5m')

            # Map period to yfinance period - get more data for better indicators
            requested_period = max(period, 100)  # Minimum 100 bars for indicators

            if timeframe in ['1m', '5m']:
                yf_period = '7d'  # Max for minute data
            elif timeframe == '15m':
                yf_period = '60d'
            elif timeframe == '1h':
                yf_period = '730d'
            else:
                yf_period = '2y'

            ticker = yf.Ticker(symbol)
            df = ticker.history(period=yf_period, interval=interval)

            if df.empty:
                return pd.DataFrame()

            # Standardize column names
            df.columns = df.columns.str.lower()
            df = df[['open', 'high', 'low', 'close', 'volume']]

            # Return at least the requested period, but more if available for indicators
            if len(df) > requested_period:
                df = df.tail(requested_period)

            return df

        except Exception as e:
            logger.error(f"Error getting yfinance data for {symbol}: {e}")
            return pd.DataFrame()



    def get_fundamental_data(self, symbol: str) -> Optional[Dict]:
        """Get fundamental data for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_company_profile(symbol)
        return None

    def get_financial_ratios(self, symbol: str) -> Optional[Dict]:
        """Get financial ratios for a symbol"""
        if self.fmp_client:
            return self.fmp_client.get_financial_ratios(symbol)
        return None

    def get_latest_quote(self, symbol: str) -> Optional[Dict]:
        """Get latest quote for a symbol"""
        if self.alpaca_client:
            return self.alpaca_client.get_latest_quote(symbol)
        return None

    def is_market_open(self) -> bool:
        """Check if market is open"""
        if self.alpaca_client:
            return self.alpaca_client.is_market_open()

        # Fallback: simple time-based check
        now = datetime.now()
        if now.weekday() >= 5:  # Weekend
            return False

        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)

        return market_open <= now <= market_close

# Backward compatibility function
def get_stock_data(symbol: str, timeframe: str = '5m', period: int = 100) -> pd.DataFrame:
    """
    Backward compatible function for existing code
    Returns REAL market data only - NO SIMULATED DATA
    """
    data_feed = DataFeed()
    return data_feed.get_stock_data(symbol, timeframe, period)
