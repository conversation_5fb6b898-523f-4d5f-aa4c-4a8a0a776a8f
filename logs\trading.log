2025-06-11 19:00:59,076 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,076 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,127 - api.alpaca_client - ERROR - get_account_info:50 - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-11 19:00:59,127 - api.alpaca_client - ERROR - get_account_info:50 - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-11 19:00:59,867 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,867 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:00,127 - data_feed - INFO - __init__:26 - <PERSON><PERSON><PERSON> initialized successfully
2025-06-11 19:01:00,127 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:32,782 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:32,782 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,613 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,613 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,925 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:33,925 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:29,159 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:29,159 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:30,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:30,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,273 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,273 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,497 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:31,497 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:53,208 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:53,209 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:54,367 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:54,367 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:55,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:55,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:57,587 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:57,587 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:03:04,480 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,481 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:03:04,632 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,632 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,868 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:03:04,868 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:18,384 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:18,384 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:06:18,997 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:18,997 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,432 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,432 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,656 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:19,656 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:54,334 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,334 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,714 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,714 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,955 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:54,955 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 03:54:19,304 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,304 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,781 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,781 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:20,007 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 03:54:20,007 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:42,893 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:42,893 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:43,248 - api.alpaca_client - WARNING - get_historical_data:172 - No data returned for AAPL
2025-06-12 06:34:43,248 - api.alpaca_client - WARNING - get_historical_data:172 - No data returned for AAPL
2025-06-12 06:34:43,976 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:43,976 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:44,228 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:44,228 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
