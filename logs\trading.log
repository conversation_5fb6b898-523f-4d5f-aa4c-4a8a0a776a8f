2025-06-11 19:00:59,076 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,076 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,127 - api.alpaca_client - ERROR - get_account_info:50 - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-11 19:00:59,127 - api.alpaca_client - ERROR - get_account_info:50 - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-11 19:00:59,867 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,867 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:00,127 - data_feed - INFO - __init__:26 - <PERSON><PERSON><PERSON> initialized successfully
2025-06-11 19:01:00,127 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:32,782 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:32,782 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,613 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,613 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,925 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:33,925 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:29,159 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:29,159 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:30,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:30,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,273 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,273 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,497 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:31,497 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:53,208 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:53,209 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:54,367 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:54,367 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:55,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:55,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:57,587 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:57,587 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:03:04,480 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,481 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:03:04,632 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,632 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,868 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:03:04,868 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:18,384 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:18,384 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:06:18,997 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:18,997 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,432 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,432 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,656 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:19,656 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:54,334 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,334 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,714 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,714 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,955 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:54,955 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 03:54:19,304 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,304 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,781 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,781 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:20,007 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 03:54:20,007 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:42,893 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:42,893 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:43,248 - api.alpaca_client - WARNING - get_historical_data:172 - No data returned for AAPL
2025-06-12 06:34:43,248 - api.alpaca_client - WARNING - get_historical_data:172 - No data returned for AAPL
2025-06-12 06:34:43,976 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:43,976 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:44,228 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:44,228 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:11,061 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,061 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,486 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,486 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,724 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:11,724 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:17,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:17,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:17,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 08:26:17,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 08:26:17,875 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:17,875 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:18,124 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:18,124 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:18,497 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:18,497 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:18,739 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:18,739 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,043 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,043 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,288 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,288 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,609 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,609 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,855 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,855 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:20,155 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:20,155 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:20,386 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:20,386 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:20,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:20,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:20,976 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:20,976 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:21,311 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:21,311 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:21,535 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:21,535 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:21,842 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:21,842 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,067 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,067 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,380 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,380 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,604 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,604 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,919 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,919 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:23,148 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:23,148 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:23,476 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:23,476 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:23,721 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:23,721 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,053 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,053 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,306 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,306 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,626 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,626 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,856 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,856 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:25,181 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:25,181 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:25,513 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:25,513 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:25,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:25,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:26,081 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:26,081 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:26,408 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:26,408 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:26,645 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:26,645 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:26,971 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:26,971 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:27,224 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:27,224 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:27,552 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:27,552 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:27,785 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:27,785 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:28,104 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:28,104 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:28,340 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:28,340 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:28,652 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:28,652 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:29,014 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:29,014 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:29,344 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:29,344 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:29,646 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:29,646 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,065 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,065 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,310 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,310 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,633 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,633 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,891 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,891 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:31,283 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:31,283 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:31,515 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:31,515 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:31,866 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:31,866 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:32,128 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:32,128 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:32,537 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:32,537 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:32,819 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:32,819 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,150 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,150 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,403 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,403 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,715 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,715 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,950 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,950 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:34,270 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:34,270 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:34,527 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:34,527 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:34,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:34,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:35,063 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:35,063 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:34:58,553 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:58,553 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:58,944 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:58,944 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:59,189 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:34:59,189 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:12,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:12,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:12,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 09:13:12,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 09:13:12,824 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:12,824 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:13,103 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:13,103 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,219 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,219 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,448 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,448 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,966 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,966 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:15,276 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:15,276 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:15,877 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:15,877 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:16,177 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:16,177 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:16,395 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:16,395 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:17,972 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:17,972 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:18,255 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:18,255 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:18,573 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:18,573 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:18,803 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:18,803 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:19,103 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:19,103 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:19,609 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:19,609 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:19,932 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:19,932 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:20,202 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:20,202 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:20,516 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:20,516 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:20,816 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:20,816 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
