import time
from screener import scan_market
from trade_executor import execute_trade
from risk_manager import manage_positions
from pnl_tracker import track_pnl
from config import config

if __name__ == '__main__':
    print("[AI Trader] Starting main loop...")

    while True:
        print("[Scan] Looking for new trade setups...")
        signals = scan_market()

        for signal in signals:
            print(f"[Trade] Signal found: {signal['symbol']} | Side: {signal['side']}")
            execute_trade(signal)

        manage_positions()
        track_pnl()

        print("[Sleep] Waiting before next scan...")
        time.sleep(config['scan_interval'])
