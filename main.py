"""
Enhanced AI AutoTrader Main Application
"""
import time
import argparse
from datetime import datetime
from utils.logger import setup_logging, get_logger
from screener import scan_market
from trade_executor import execute_trade
from risk_manager import manage_positions
from pnl_tracker import track_pnl
from data_feed import DataFeed
from config import TRADING_CONFIG, ALPACA_CONFIG

def run_live_trading():
    """Run live trading mode"""
    logger = get_logger('main')
    logger.info("Starting AI AutoTrader in LIVE mode...")

    # Initialize components
    data_feed = DataFeed()

    # Check market status
    if not data_feed.is_market_open():
        logger.warning("Market is currently closed. Consider using backtest mode.")
        print("⚠️  Market is currently closed!")
        print("💡 Tip: Use 'python main.py --mode backtest' to test strategies with historical data")
        return

    print("🚀 AI AutoTrader - Live Trading Mode")
    print("=" * 50)
    print(f"📊 Scan Interval: {TRADING_CONFIG['scan_interval']} seconds")
    print(f"💰 Trade Quantity: {TRADING_CONFIG['trade_quantity']} shares")
    print(f"🎯 Take Profit: ${TRADING_CONFIG['take_profit']}")
    print(f"🛡️  Stop Loss: ${TRADING_CONFIG['stop_loss']}")
    print("=" * 50)

    try:
        iteration = 0
        while True:
            iteration += 1
            logger.info(f"Starting trading iteration {iteration}")

            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] Scan #{iteration}")
            print("🔍 Looking for new trade setups...")

            # Scan for signals
            signals = scan_market()

            if signals:
                print(f"📈 Found {len(signals)} signal(s)")
                for signal in signals:
                    print(f"   • {signal['symbol']} | {signal['side'].upper()} | ${signal['price']:.2f}")

                    # Execute trade
                    result = execute_trade(signal)
                    if result:
                        print(f"   ✅ Order placed: {result.get('id', 'N/A')}")
                    else:
                        print(f"   ❌ Failed to place order")
            else:
                print("📊 No signals found")

            # Risk management
            print("🛡️  Checking risk management...")
            manage_positions()

            # Update P&L
            print("💰 Updating P&L...")
            track_pnl()

            # Check if market is still open
            if not data_feed.is_market_open():
                logger.info("Market closed, stopping live trading")
                print("🔔 Market closed. Stopping live trading.")
                break

            print(f"⏰ Waiting {TRADING_CONFIG['scan_interval']} seconds...")
            time.sleep(TRADING_CONFIG['scan_interval'])

    except KeyboardInterrupt:
        logger.info("Trading stopped by user")
        print("\n🛑 Trading stopped by user")
    except Exception as e:
        logger.error(f"Error in live trading: {e}")
        print(f"\n❌ Error: {e}")

def run_backtest():
    """Run backtesting mode"""
    logger = get_logger('main')
    logger.info("Starting AI AutoTrader in BACKTEST mode...")

    print("📊 AI AutoTrader - Backtesting Mode")
    print("=" * 50)
    print("🚧 Backtesting engine will be implemented in Phase 2")
    print("💡 For now, you can run live trading simulation with paper trading")
    print("=" * 50)

def run_gui():
    """Run GUI mode"""
    logger = get_logger('main')
    logger.info("Starting AI AutoTrader in GUI mode...")

    print("🖥️  AI AutoTrader - GUI Mode")
    print("=" * 50)
    print("🚧 GUI interface will be implemented in Phase 4")
    print("💡 For now, use command line modes")
    print("=" * 50)

def main():
    """Main application entry point"""
    # Setup logging
    setup_logging()
    logger = get_logger('main')

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='AI AutoTrader - Automated Trading System')
    parser.add_argument('--mode', choices=['live', 'backtest', 'gui'],
                       default='live', help='Trading mode (default: live)')
    parser.add_argument('--test', action='store_true',
                       help='Run API connection tests')

    args = parser.parse_args()

    # Run tests if requested
    if args.test:
        print("🧪 Running API connection tests...")
        import subprocess
        result = subprocess.run(['python', 'test_api_connections.py'],
                              capture_output=True, text=True)
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return

    # Display startup banner
    print("\n" + "=" * 60)
    print("🤖 AI AUTOTRADER PRO")
    print("   Automated Trading System with AI Signal Verification")
    print("=" * 60)
    print(f"📅 Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🔧 Mode: {args.mode.upper()}")
    print(f"🔑 API: {ALPACA_CONFIG['base_url']}")
    print("=" * 60)

    # Run appropriate mode
    try:
        if args.mode == 'live':
            run_live_trading()
        elif args.mode == 'backtest':
            run_backtest()
        elif args.mode == 'gui':
            run_gui()
    except Exception as e:
        logger.error(f"Application error: {e}")
        print(f"\n💥 Application Error: {e}")
        print("📋 Check logs/trading_system.log for details")

if __name__ == '__main__':
    main()
