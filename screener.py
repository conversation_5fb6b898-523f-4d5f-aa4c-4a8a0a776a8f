import pandas as pd
import numpy as np
from data_feed import get_stock_data

def scan_market():
    universe = ['AAPL', 'MSFT', 'TSLA', 'AMZN', 'GOOGL']
    signals = []

    for symbol in universe:
        try:
            df = get_stock_data(symbol)
            df = calculate_indicators(df)

            if is_entry_signal(df):
                signals.append({
                    'symbol': symbol,
                    'side': 'buy',
                    'price': df['close'].iloc[-1],
                    'time': df.index[-1]
                })
        except Exception as e:
            print(f"[Error] Scanning {symbol}: {e}")

    return signals

def calculate_indicators(df):
    df['momentum'] = df['close'] - df['close'].shift(5)
    df['hist_color'] = np.where(df['momentum'] < 0, 'red',
                         np.where(df['momentum'] > 0, 'yellow', 'gray'))

    df['stddev'] = df['close'].rolling(20).std()
    tr = pd.concat([
        df['high'] - df['low'],
        abs(df['high'] - df['close'].shift(1)),
        abs(df['low'] - df['close'].shift(1))
    ], axis=1).max(axis=1)
    df['atr'] = tr.rolling(14).mean()
    df['atr_stop'] = df['close'] - df['atr'] * 3
    df['atr_signal'] = np.where(df['close'] > df['atr_stop'], 'blue', 'pink')

    return df

def is_entry_signal(df):
    last5 = df.tail(6)
    reds = last5['hist_color'].iloc[:4]
    turning = last5['hist_color'].iloc[4] == 'yellow'
    declining = all(last5['momentum'].iloc[i] < last5['momentum'].iloc[i-1] for i in range(1, 4))
    atrts = last5['atr_signal'].iloc[4] == 'blue'

    return all(reds == 'red') and declining and turning and atrts
