"""
TTM Squeeze Strategy Screener
Implements the exact strategy shown in the GLD chart example
"""
import pandas as pd
import numpy as np
from data_feed import get_stock_data
from utils.logger import get_logger
# Stock universe imports moved to function level to avoid unused import warnings

logger = get_logger('screener')

def scan_market():
    """
    Scan market for TTM Squeeze breakout signals - OPTIMIZED for real-time scanning
    """
    # Start with high-priority symbols for faster scanning
    from stock_universe import get_tech_heavy, get_etfs_only

    # Priority 1: Tech stocks + Major ETFs (most active)
    priority_symbols = get_tech_heavy() + get_etfs_only()

    # Priority 2: Add some major S&P 500 names
    major_sp500 = [
        'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK.B', 'UNH', 'JNJ',
        'V', 'PG', 'JPM', 'MA', 'HD', 'CVX', 'LLY', 'ABBV', 'PFE', 'KO',
        'AVGO', 'XOM', 'BAC', 'WMT', 'COST', 'DIS', 'ADBE', 'NFLX', 'CRM', 'TMO'
    ]

    # Combine and remove duplicates
    universe = list(set(priority_symbols + major_sp500))
    universe.sort()  # Consistent order

    signals = []

    logger.info(f"OPTIMIZED MARKET SCAN INITIATED")
    logger.info(f"Scanning {len(universe)} high-priority symbols")
    logger.info(f"Focus: Tech stocks, ETFs, and major S&P 500 names")
    logger.info(f"Optimized for speed and signal quality")

    scan_count = 0
    error_count = 0

    for i, symbol in enumerate(universe, 1):
        try:
            # Progress logging every 10 symbols for better feedback
            if i % 10 == 0:
                logger.info(f"Progress: {i}/{len(universe)} symbols scanned ({(i/len(universe)*100):.1f}%)")

            # Get data optimized for current market conditions
            # Try different timeframes to get more data
            df = None

            # Try hourly first (more likely to have data)
            for timeframe, min_bars in [('1h', 20), ('1d', 10), ('5m', 15)]:
                df = get_stock_data(symbol, timeframe=timeframe, period=100)
                if not df.empty and len(df) >= min_bars:
                    logger.debug(f"Got {len(df)} bars for {symbol} on {timeframe}")
                    break

            if df is None or df.empty or len(df) < 10:
                logger.debug(f"Insufficient data for {symbol} ({len(df) if df is not None and not df.empty else 0} bars)")
                continue

            scan_count += 1

            # Calculate indicators with error handling
            try:
                df = calculate_ttm_squeeze_indicators(df)
                signal = detect_ttm_squeeze_breakout(df, symbol)

                if signal:
                    signals.append(signal)
                    logger.info(f"TTM Squeeze signal detected: {symbol}")
                    logger.info(f"   Price: ${signal['price']:.2f} | Confidence: {signal['confidence']:.3f}")

            except Exception as calc_error:
                logger.debug(f"Indicator calculation error for {symbol}: {calc_error}")
                continue

        except Exception as e:
            error_count += 1
            logger.debug(f"Error scanning {symbol}: {e}")

            # Log major errors but don't spam
            if error_count <= 3:
                logger.warning(f"Scan error for {symbol}: {e}")
            elif error_count == 4:
                logger.warning(f"Additional scan errors suppressed (total: {error_count}+)")

    logger.info(f"COMPREHENSIVE SCAN COMPLETE")
    logger.info(f"Results: {len(signals)} signals found from {scan_count} valid scans")
    logger.info(f"Errors: {error_count} symbols had data/processing issues")
    logger.info(f"Coverage: {scan_count}/{len(universe)} symbols successfully analyzed")

    return signals

def calculate_ttm_squeeze_indicators(df):
    """
    Calculate TTM Squeeze indicators exactly as shown in the chart
    """
    # Bollinger Bands (20-period, 2 std dev)
    df['bb_basis'] = df['close'].rolling(20).mean()
    df['bb_std'] = df['close'].rolling(20).std()
    df['bb_upper'] = df['bb_basis'] + (2 * df['bb_std'])
    df['bb_lower'] = df['bb_basis'] - (2 * df['bb_std'])

    # Keltner Channels (20-period EMA, 1.5 * ATR)
    df['kc_basis'] = df['close'].ewm(span=20).mean()

    # True Range and ATR
    df['tr'] = np.maximum(
        df['high'] - df['low'],
        np.maximum(
            abs(df['high'] - df['close'].shift(1)),
            abs(df['low'] - df['close'].shift(1))
        )
    )
    df['atr'] = df['tr'].rolling(20).mean()

    df['kc_upper'] = df['kc_basis'] + (1.5 * df['atr'])
    df['kc_lower'] = df['kc_basis'] - (1.5 * df['atr'])

    # TTM Squeeze Ratio (2 * BB_std / ATR)
    df['squeeze_ratio'] = (2 * df['bb_std']) / df['atr']

    # Squeeze condition: BB inside KC (ratio < 2.0)
    df['squeeze'] = df['squeeze_ratio'] < 2.0

    # Momentum calculation (Linear Regression of close vs time)
    def calculate_momentum(series, period=20):
        momentum = []
        for i in range(len(series)):
            if i < period - 1:
                momentum.append(np.nan)
            else:
                y = series.iloc[i-period+1:i+1].values
                x = np.arange(period)
                # Linear regression slope
                slope = np.polyfit(x, y, 1)[0]
                momentum.append(slope)
        return pd.Series(momentum, index=series.index)

    df['momentum'] = calculate_momentum(df['close'])

    # Histogram colors based on momentum direction
    df['hist_color'] = np.where(
        df['momentum'] < 0, 'red',
        np.where(df['momentum'] > 0, 'yellow', 'gray')
    )

    # ATR Trailing Stop (similar to your chart's blue/pink dots)
    atr_multiplier = 2.0
    df['atr_stop_long'] = df['close'] - (atr_multiplier * df['atr'])
    df['atr_stop_short'] = df['close'] + (atr_multiplier * df['atr'])

    # Determine if price is above/below ATR stop
    df['above_atr_stop'] = df['close'] > df['atr_stop_long']
    df['atr_signal'] = np.where(df['above_atr_stop'], 'blue', 'pink')

    return df

def detect_ttm_squeeze_breakout(df, symbol):
    """
    Detect the EXACT TTM Squeeze breakout pattern from the GLD chart:

    PRECISE ENTRY CRITERIA:
    1. Four consecutive RED histogram bars (declining momentum slope)
    2. Followed immediately by first YELLOW histogram bar (momentum turning positive)
    3. Price ABOVE ATR trailing stop on that yellow bar (BLUE signal, not pink)
    4. Coming out of or recently out of SQUEEZE condition (BB inside KC, ratio < 2.0)
    5. Volume confirmation (≥80% of 20-period average)

    ENTRY POINT: The exact yellow bar after red sequence with blue ATR confirmation
    """
    if len(df) < 25:  # Need enough data for proper calculation
        return None

    # Look at last 8 bars to analyze pattern thoroughly
    recent = df.tail(8).copy()

    # Ensure we have valid data for all indicators
    required_cols = ['hist_color', 'atr_signal', 'momentum', 'squeeze_ratio', 'squeeze']
    if any(recent[col].isna().any() for col in required_cols):
        logger.debug(f"Insufficient indicator data for {symbol}")
        return None

    try:
        # CRITICAL PATTERN ANALYSIS

        # 1. FOUR CONSECUTIVE RED BARS (positions -6 to -3 relative to current)
        # Looking at bars before the potential yellow signal bar
        red_sequence = recent['hist_color'].iloc[2:6]  # 4 bars before current
        four_consecutive_reds = all(red_sequence == 'red')

        # 2. FIRST YELLOW BAR (position -2, the signal bar)
        signal_bar_yellow = recent['hist_color'].iloc[6] == 'yellow'

        # 3. MOMENTUM SLOPE ANALYSIS - Must show declining then turning up
        momentum_vals = recent['momentum'].iloc[2:7]  # Include signal bar

        # Red bars should show declining momentum (becoming more negative)
        declining_momentum = True
        for i in range(1, 4):  # Check 3 transitions in red sequence
            if momentum_vals.iloc[i] >= momentum_vals.iloc[i-1]:  # Should be getting more negative
                declining_momentum = False
                break

        # Yellow bar should show momentum turning up (less negative or positive)
        momentum_turning_up = momentum_vals.iloc[4] > momentum_vals.iloc[3]

        # 4. ATR TRAILING STOP CONFIRMATION - BLUE on yellow bar
        atr_blue_on_signal = recent['atr_signal'].iloc[6] == 'blue'

        # 5. SQUEEZE CONDITION - Must be coming out of squeeze
        current_ratio = recent['squeeze_ratio'].iloc[6]

        # Check if we were recently in squeeze (any of last 6 bars)
        was_recently_squeezed = any(recent['squeeze'].iloc[0:6])

        # Prefer signals near squeeze exit (ratio between 1.8 and 2.5)
        near_squeeze_exit = 1.8 <= current_ratio <= 2.5

        # 6. VOLUME CONFIRMATION
        volume_confirmed = True
        if 'volume' in df.columns and len(df) >= 20:
            avg_volume_20 = df['volume'].rolling(20).mean().iloc[-1]
            current_volume = df['volume'].iloc[-1]
            volume_confirmed = current_volume >= (avg_volume_20 * 0.8)

        # 7. ADDITIONAL QUALITY FILTERS

        # Ensure we're not in middle of expansion (avoid false signals)
        not_in_expansion = current_ratio < 3.0  # Don't trigger in strong expansion

        # Momentum should be crossing from negative to positive territory
        momentum_cross = (momentum_vals.iloc[3] < 0 and momentum_vals.iloc[4] >= momentum_vals.iloc[3])

        # Price action confirmation - close should be near highs
        recent_price = df.tail(3)
        price_strength = recent_price['close'].iloc[-1] >= recent_price['low'].iloc[-1] + 0.7 * (recent_price['high'].iloc[-1] - recent_price['low'].iloc[-1])

        # FINAL SIGNAL VALIDATION
        all_conditions_met = (
            four_consecutive_reds and           # 4 red bars
            signal_bar_yellow and              # Yellow signal bar
            declining_momentum and             # Momentum was declining
            momentum_turning_up and            # Momentum turning up
            atr_blue_on_signal and            # Price above ATR stop
            was_recently_squeezed and         # Coming out of squeeze
            near_squeeze_exit and             # Near optimal ratio
            volume_confirmed and              # Volume support
            not_in_expansion and              # Not in strong expansion
            momentum_cross and                # Momentum crossing up
            price_strength                    # Price near highs
        )

        if all_conditions_met:
            # Calculate enhanced confidence score
            confidence = calculate_enhanced_confidence(recent, df)

            signal = {
                'symbol': symbol,
                'side': 'buy',
                'price': df['close'].iloc[-1],
                'time': df.index[-1],
                'confidence': confidence,
                'squeeze_ratio': current_ratio,
                'momentum': recent['momentum'].iloc[6],
                'momentum_change': momentum_vals.iloc[4] - momentum_vals.iloc[3],
                'volume_ratio': current_volume / avg_volume_20 if 'volume' in df.columns else 1.0,
                'pattern': 'TTM_Squeeze_Breakout_GLD_Style',
                'entry_reason': 'First yellow bar after 4 reds with blue ATR confirmation'
            }

            logger.info(f"🎯 PRECISE TTM Squeeze breakout detected for {symbol}:")
            logger.info(f"  📍 Entry Price: ${signal['price']:.2f}")
            logger.info(f"  🎯 Confidence: {signal['confidence']:.3f}")
            logger.info(f"  📊 Squeeze Ratio: {current_ratio:.2f}")
            logger.info(f"  📈 Momentum: {signal['momentum']:.6f}")
            logger.info(f"  🔄 Momentum Change: {signal['momentum_change']:.6f}")
            logger.info(f"  📊 Volume Ratio: {signal['volume_ratio']:.2f}")
            logger.info(f"  ✅ Pattern: 4 RED → YELLOW + BLUE ATR")

            return signal

        else:
            # Log why signal was rejected for debugging
            reasons = []
            if not four_consecutive_reds: reasons.append("Missing 4 consecutive reds")
            if not signal_bar_yellow: reasons.append("Signal bar not yellow")
            if not declining_momentum: reasons.append("Momentum not declining properly")
            if not momentum_turning_up: reasons.append("Momentum not turning up")
            if not atr_blue_on_signal: reasons.append("ATR not blue on signal bar")
            if not was_recently_squeezed: reasons.append("Not coming out of squeeze")
            if not near_squeeze_exit: reasons.append("Not near squeeze exit ratio")
            if not volume_confirmed: reasons.append("Volume too low")
            if not not_in_expansion: reasons.append("Already in expansion")
            if not momentum_cross: reasons.append("No momentum cross")
            if not price_strength: reasons.append("Weak price action")

            logger.debug(f"Signal rejected for {symbol}: {', '.join(reasons)}")

    except Exception as e:
        logger.error(f"Error detecting TTM Squeeze pattern for {symbol}: {e}")

    return None

def calculate_enhanced_confidence(recent_data, full_df):
    """
    Calculate enhanced confidence score for TTM Squeeze breakout (0.0 to 1.0)
    Based on multiple quality factors from the GLD chart pattern
    """
    confidence = 0.0

    # Base confidence for meeting the core pattern (40%)
    confidence += 0.40

    # 1. MOMENTUM QUALITY (20% max)
    momentum_vals = recent_data['momentum'].iloc[2:7]

    # Quality of declining momentum in red bars
    decline_quality = 0
    for i in range(1, 4):
        if momentum_vals.iloc[i] < momentum_vals.iloc[i-1]:
            decline_quality += 1
    decline_score = (decline_quality / 3) * 0.10  # Up to 10%

    # Strength of momentum turn
    momentum_change = momentum_vals.iloc[4] - momentum_vals.iloc[3]
    turn_strength = min(0.10, abs(momentum_change) * 1000)  # Up to 10%

    confidence += decline_score + turn_strength

    # 2. SQUEEZE RATIO QUALITY (15% max)
    current_ratio = recent_data['squeeze_ratio'].iloc[6]

    # Optimal ratio range (1.9 to 2.1 is perfect squeeze exit)
    if 1.9 <= current_ratio <= 2.1:
        confidence += 0.15  # Perfect range
    elif 1.8 <= current_ratio <= 2.3:
        confidence += 0.10  # Good range
    elif 1.7 <= current_ratio <= 2.5:
        confidence += 0.05  # Acceptable range

    # 3. ATR SIGNAL CONSISTENCY (10% max)
    atr_signals = recent_data['atr_signal'].iloc[4:7]  # Last 3 bars
    blue_count = sum(1 for signal in atr_signals if signal == 'blue')
    atr_consistency = (blue_count / 3) * 0.10
    confidence += atr_consistency

    # 4. VOLUME STRENGTH (10% max)
    if 'volume' in full_df.columns and len(full_df) >= 20:
        avg_volume = full_df['volume'].rolling(20).mean().iloc[-1]
        current_volume = full_df['volume'].iloc[-1]
        volume_ratio = current_volume / avg_volume

        if volume_ratio >= 1.5:  # 150% of average
            confidence += 0.10
        elif volume_ratio >= 1.2:  # 120% of average
            confidence += 0.07
        elif volume_ratio >= 1.0:  # 100% of average
            confidence += 0.05
        elif volume_ratio >= 0.8:  # 80% of average
            confidence += 0.03

    # 5. PRICE ACTION QUALITY (5% max)
    recent_price = full_df.tail(3)

    # Price closing in upper portion of recent range
    high_3 = recent_price['high'].max()
    low_3 = recent_price['low'].min()
    close_position = (recent_price['close'].iloc[-1] - low_3) / (high_3 - low_3) if high_3 != low_3 else 0.5

    if close_position >= 0.8:  # Closing in top 20%
        confidence += 0.05
    elif close_position >= 0.6:  # Closing in top 40%
        confidence += 0.03

    return min(1.0, confidence)  # Cap at 100%

def calculate_signal_confidence(recent_data):
    """
    Legacy confidence calculation for backward compatibility
    """
    confidence = 0.0

    # Base confidence for meeting basic pattern
    confidence += 0.4

    # Bonus for strong momentum increase
    momentum_change = recent_data['momentum'].iloc[4] - recent_data['momentum'].iloc[2]
    if momentum_change > 0:
        confidence += min(0.2, momentum_change * 100)  # Cap at 0.2

    # Bonus for being close to squeeze exit
    current_ratio = recent_data['squeeze_ratio'].iloc[4]
    if 1.8 <= current_ratio <= 2.2:  # Sweet spot near squeeze exit
        confidence += 0.2

    # Bonus for consistent ATR signal
    atr_signals = recent_data['atr_signal'].iloc[3:5]
    if all(atr_signals == 'blue'):
        confidence += 0.1

    # Bonus for clean momentum progression
    momentum_values = recent_data['momentum'].iloc[2:5]
    if all(momentum_values.iloc[i] > momentum_values.iloc[i-1] for i in range(1, len(momentum_values))):
        confidence += 0.1

    return min(1.0, confidence)  # Cap at 1.0

# Legacy function for backward compatibility
def calculate_indicators(df):
    """Legacy function - redirects to TTM Squeeze calculation"""
    return calculate_ttm_squeeze_indicators(df)

def is_entry_signal(df):
    """Legacy function - redirects to TTM Squeeze detection"""
    signal = detect_ttm_squeeze_breakout(df, 'UNKNOWN')
    return signal is not None
