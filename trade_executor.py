"""
Enhanced Trade Executor with Alpaca API integration
"""
import logging
from typing import Dict, Optional
from config import TRADING_CONFIG
from api.alpaca_client import AlpacaClient

logger = logging.getLogger(__name__)

class TradeExecutor:
    """
    Trade execution engine with real API integration
    """

    def __init__(self):
        """Initialize trade executor"""
        try:
            self.alpaca_client = AlpacaClient()
            self.config = TRADING_CONFIG
            logger.info("TradeExecutor initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing TradeExecutor: {e}")
            self.alpaca_client = None

    def execute_trade(self, signal: Dict) -> Optional[Dict]:
        """
        Execute a trade based on signal

        Args:
            signal: Dictionary containing trade signal information
                   - symbol: Stock symbol
                   - side: 'buy' or 'sell'
                   - price: Current price
                   - confidence: AI confidence score (optional)
                   - quantity: Number of shares (optional)

        Returns:
            Dictionary with order information or None if failed
        """
        try:
            symbol = signal['symbol']
            side = signal['side']
            price = signal.get('price', 0)
            quantity = signal.get('quantity', self.config['trade_quantity'])

            logger.info(f"Executing {side} order for {quantity} shares of {symbol} at ${price:.2f}")

            # Check if we have API connection
            if not self.alpaca_client:
                logger.error("No API connection available")
                return self._simulate_order(signal)

            # Check account status
            account_info = self.alpaca_client.get_account_info()
            if not account_info:
                logger.error("Could not get account information")
                return None

            # Check if account is blocked
            if account_info.get('trading_blocked') or account_info.get('account_blocked'):
                logger.error("Trading is blocked on this account")
                return None

            # Check buying power for buy orders
            if side == 'buy':
                required_capital = quantity * price
                if required_capital > account_info.get('buying_power', 0):
                    logger.error(f"Insufficient buying power. Required: ${required_capital:.2f}, Available: ${account_info.get('buying_power', 0):.2f}")
                    return None

            # Place the order
            order = self.alpaca_client.place_order(
                symbol=symbol,
                qty=quantity,
                side=side,
                order_type='market',
                time_in_force='day'
            )

            if order:
                logger.info(f"Order placed successfully: {order['id']}")
                return order
            else:
                logger.error("Failed to place order")
                return None

        except Exception as e:
            logger.error(f"Error executing trade: {e}")
            return None

    def execute_limit_order(self, signal: Dict, limit_price: float) -> Optional[Dict]:
        """Execute a limit order"""
        try:
            symbol = signal['symbol']
            side = signal['side']
            quantity = signal.get('quantity', self.config['trade_quantity'])

            logger.info(f"Executing {side} limit order for {quantity} shares of {symbol} at ${limit_price:.2f}")

            if not self.alpaca_client:
                return self._simulate_order(signal)

            order = self.alpaca_client.place_order(
                symbol=symbol,
                qty=quantity,
                side=side,
                order_type='limit',
                time_in_force='day',
                limit_price=limit_price
            )

            if order:
                logger.info(f"Limit order placed successfully: {order['id']}")
                return order
            else:
                logger.error("Failed to place limit order")
                return None

        except Exception as e:
            logger.error(f"Error executing limit order: {e}")
            return None

    def execute_stop_loss_order(self, symbol: str, quantity: int, stop_price: float) -> Optional[Dict]:
        """Execute a stop-loss order"""
        try:
            logger.info(f"Executing stop-loss order for {quantity} shares of {symbol} at ${stop_price:.2f}")

            if not self.alpaca_client:
                return None

            order = self.alpaca_client.place_order(
                symbol=symbol,
                qty=quantity,
                side='sell',
                order_type='stop',
                time_in_force='day',
                stop_price=stop_price
            )

            if order:
                logger.info(f"Stop-loss order placed successfully: {order['id']}")
                return order
            else:
                logger.error("Failed to place stop-loss order")
                return None

        except Exception as e:
            logger.error(f"Error executing stop-loss order: {e}")
            return None

    def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            if not self.alpaca_client:
                logger.error("No API connection available")
                return False

            success = self.alpaca_client.cancel_order(order_id)
            if success:
                logger.info(f"Order {order_id} cancelled successfully")
            else:
                logger.error(f"Failed to cancel order {order_id}")

            return success

        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False

    def get_open_orders(self) -> list:
        """Get all open orders"""
        try:
            if not self.alpaca_client:
                return []

            orders = self.alpaca_client.get_orders(status='open')
            return orders

        except Exception as e:
            logger.error(f"Error getting open orders: {e}")
            return []

    def _simulate_order(self, signal: Dict) -> Dict:
        """Simulate order placement for testing"""
        import uuid

        order = {
            'id': str(uuid.uuid4()),
            'symbol': signal['symbol'],
            'qty': signal.get('quantity', self.config['trade_quantity']),
            'side': signal['side'],
            'order_type': 'market',
            'status': 'filled',
            'created_at': None
        }

        logger.info(f"[SIMULATED] Order placed: {order}")
        return order

# Initialize global trade executor
trade_executor = TradeExecutor()

# Backward compatibility function
def execute_trade(signal: Dict) -> Optional[Dict]:
    """Backward compatible function for existing code"""
    return trade_executor.execute_trade(signal)
