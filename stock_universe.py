"""
Comprehensive stock universe for TTM Squeeze scanning
Includes S&P 500 and all 100+ billion market cap stocks
"""

# S&P 500 stocks (major components)
SP500_STOCKS = [
    # Technology
    'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSL<PERSON>', 'AVGO', 'ORCL',
    'CRM', 'ADBE', 'NFLX', 'AMD', 'INTC', 'CSCO', 'ACN', 'TXN', 'QCOM', 'IBM',
    'INTU', 'NOW', 'MU', 'AMAT', 'ADI', 'LRCX', 'KLAC', 'MRVL', 'FTNT', 'PANW',
    
    # Healthcare
    'UNH', 'JNJ', 'PFE', 'ABBV', 'LLY', 'TMO', 'ABT', 'DHR', 'BMY', 'MRK',
    'AMGN', 'MDT', 'ISRG', 'GILD', 'CVS', 'CI', 'REGN', 'ZTS', 'BDX', 'SYK',
    
    # Financial Services
    'BRK.B', 'JPM', 'V', 'MA', 'BAC', 'WFC', 'GS', 'MS', 'C', 'AXP',
    'SCHW', 'BLK', 'SPGI', 'CME', 'ICE', 'MCO', 'AON', 'MMC', 'TRV', 'PGR',
    
    # Consumer Discretionary
    'AMZN', 'TSLA', 'HD', 'MCD', 'NKE', 'LOW', 'SBUX', 'TJX', 'BKNG', 'ABNB',
    'GM', 'F', 'ORLY', 'AZO', 'LULU', 'RCL', 'CCL', 'MAR', 'HLT', 'MGM',
    
    # Consumer Staples
    'PG', 'KO', 'PEP', 'WMT', 'COST', 'MDLZ', 'CL', 'KMB', 'GIS', 'K',
    'HSY', 'MKC', 'SJM', 'CAG', 'CPB', 'CHD', 'CLX', 'TSN', 'HRL', 'KHC',
    
    # Energy
    'XOM', 'CVX', 'COP', 'EOG', 'SLB', 'PSX', 'VLO', 'MPC', 'OXY', 'BKR',
    'HAL', 'DVN', 'FANG', 'EQT', 'KMI', 'WMB', 'OKE', 'LNG', 'TRGP', 'EPD',
    
    # Industrials
    'BA', 'CAT', 'RTX', 'HON', 'UPS', 'LMT', 'DE', 'MMM', 'GE', 'FDX',
    'NOC', 'GD', 'ITW', 'EMR', 'ETN', 'PH', 'CMI', 'ROK', 'DOV', 'XYL',
    
    # Materials
    'LIN', 'APD', 'SHW', 'FCX', 'NEM', 'DOW', 'DD', 'PPG', 'ECL', 'IFF',
    'ALB', 'CE', 'FMC', 'VMC', 'MLM', 'NUE', 'STLD', 'PKG', 'IP', 'WRK',
    
    # Real Estate
    'AMT', 'PLD', 'CCI', 'EQIX', 'PSA', 'WELL', 'DLR', 'O', 'SBAC', 'EXR',
    'AVB', 'EQR', 'VTR', 'ESS', 'MAA', 'UDR', 'CPT', 'FRT', 'BXP', 'KIM',
    
    # Utilities
    'NEE', 'SO', 'DUK', 'AEP', 'SRE', 'D', 'PEG', 'EXC', 'XEL', 'ED',
    'WEC', 'AWK', 'DTE', 'ES', 'FE', 'EIX', 'ETR', 'CMS', 'CNP', 'NI',
    
    # Communication Services
    'GOOGL', 'META', 'NFLX', 'DIS', 'CMCSA', 'VZ', 'T', 'CHTR', 'TMUS', 'PARA',
    'WBD', 'EA', 'ATVI', 'TTWO', 'NWSA', 'FOXA', 'IPG', 'OMC', 'DISH', 'SIRI'
]

# Additional 100+ Billion Market Cap Stocks (not in S&P 500)
LARGE_CAP_STOCKS = [
    # International/ADRs
    'TSM', 'ASML', 'NVO', 'SHEL', 'UL', 'NVS', 'RHHBY', 'SAP', 'TM', 'SONY',
    'BABA', 'PDD', 'JD', 'BIDU', 'NIO', 'LI', 'XPEV', 'TME', 'BILI', 'IQ',
    
    # Crypto/Fintech
    'COIN', 'SQ', 'PYPL', 'HOOD', 'SOFI', 'AFRM', 'UPST', 'LC', 'OPEN', 'RBLX',
    
    # Biotech/Pharma
    'MRNA', 'BNTX', 'VRTX', 'BIIB', 'ILMN', 'MELI', 'DXCM', 'ALGN', 'IDXX', 'MTD',
    
    # Growth/Tech
    'SHOP', 'SNOW', 'DDOG', 'CRWD', 'ZM', 'OKTA', 'TWLO', 'NET', 'FSLY', 'ESTC',
    'PLTR', 'U', 'PATH', 'GTLB', 'S', 'WORK', 'TEAM', 'ATLASSIAN', 'WDAY', 'VEEV'
]

# ETFs for sector/market analysis
MAJOR_ETFS = [
    # Broad Market
    'SPY', 'QQQ', 'IWM', 'VTI', 'VEA', 'VWO', 'EFA', 'EEM',
    
    # Sector ETFs
    'XLK', 'XLF', 'XLV', 'XLE', 'XLI', 'XLY', 'XLP', 'XLB', 'XLU', 'XLRE',
    
    # Style ETFs
    'VUG', 'VTV', 'VBR', 'VBK', 'VOO', 'VXF', 'VB', 'VO',
    
    # Commodity/Alternative
    'GLD', 'SLV', 'USO', 'UNG', 'TLT', 'IEF', 'HYG', 'LQD', 'VNQ', 'REIT'
]

def get_full_universe():
    """Get the complete scanning universe"""
    # Combine all lists and remove duplicates
    full_universe = list(set(SP500_STOCKS + LARGE_CAP_STOCKS + MAJOR_ETFS))
    
    # Sort alphabetically for consistent scanning order
    full_universe.sort()
    
    return full_universe

def get_sp500_only():
    """Get S&P 500 stocks only"""
    return sorted(list(set(SP500_STOCKS)))

def get_large_cap_only():
    """Get 100+ billion market cap stocks only"""
    return sorted(list(set(LARGE_CAP_STOCKS)))

def get_etfs_only():
    """Get major ETFs only"""
    return sorted(list(set(MAJOR_ETFS)))

def get_tech_heavy():
    """Get tech-heavy universe for high-momentum scanning"""
    tech_stocks = [
        'AAPL', 'MSFT', 'GOOGL', 'GOOG', 'AMZN', 'NVDA', 'META', 'TSLA', 'NFLX', 'AMD',
        'INTC', 'CSCO', 'ORCL', 'CRM', 'ADBE', 'AVGO', 'TXN', 'QCOM', 'INTU', 'NOW',
        'SHOP', 'SNOW', 'CRWD', 'DDOG', 'ZM', 'OKTA', 'TWLO', 'NET', 'PLTR', 'U',
        'QQQ', 'XLK', 'VUG', 'ARKK', 'ARKQ', 'ARKW'
    ]
    return sorted(list(set(tech_stocks)))

def get_universe_stats():
    """Get statistics about the scanning universe"""
    full = get_full_universe()
    sp500 = get_sp500_only()
    large_cap = get_large_cap_only()
    etfs = get_etfs_only()
    
    return {
        'total_symbols': len(full),
        'sp500_count': len(sp500),
        'large_cap_count': len(large_cap),
        'etf_count': len(etfs),
        'full_universe': full
    }

# Default universe for scanning
DEFAULT_UNIVERSE = get_full_universe()

if __name__ == "__main__":
    stats = get_universe_stats()
    print("📊 Stock Universe Statistics:")
    print(f"  Total Symbols: {stats['total_symbols']}")
    print(f"  S&P 500: {stats['sp500_count']}")
    print(f"  Large Cap (100B+): {stats['large_cap_count']}")
    print(f"  Major ETFs: {stats['etf_count']}")
    print(f"\n🎯 Ready to scan {stats['total_symbols']} symbols for TTM Squeeze patterns!")
