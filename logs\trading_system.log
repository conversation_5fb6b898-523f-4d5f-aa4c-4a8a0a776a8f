2025-06-11 19:00:54,985 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:00:58,860 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:00:58,861 - test_alpaca - INFO - test_alpaca_connection:14 - Testing Alpaca API connection...
2025-06-11 19:00:59,076 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:00:59,127 - api.alpaca_client - ERROR - get_account_info:50 - Error getting account info: 'Account' object has no attribute 'day_trade_count'
2025-06-11 19:00:59,129 - test_alpaca - ERROR - test_alpaca_connection:27 - Failed to get account information
2025-06-11 19:00:59,130 - test_fmp - INFO - test_fmp_connection:52 - Testing FMP API connection...
2025-06-11 19:00:59,437 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:00:59,580 - test_fmp - INFO - test_fmp_connection:60 - Company: Apple Inc.
2025-06-11 19:00:59,582 - test_fmp - INFO - test_fmp_connection:61 - Sector: Technology
2025-06-11 19:00:59,583 - test_fmp - INFO - test_fmp_connection:62 - Market Cap: $2,968,938,324,000
2025-06-11 19:00:59,717 - test_fmp - INFO - test_fmp_connection:69 - P/E Ratio: 37.***************
2025-06-11 19:00:59,718 - test_fmp - INFO - test_fmp_connection:70 - ROE: 1.****************
2025-06-11 19:00:59,719 - test_fmp - INFO - test_fmp_connection:74 - FMP API test completed successfully
2025-06-11 19:00:59,721 - test_data_feed - INFO - test_data_feed:84 - Testing DataFeed...
2025-06-11 19:00:59,867 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:00,125 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:01:00,127 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:00,390 - test_data_feed - INFO - test_data_feed:95 - AAPL: Retrieved 10 records, Latest close: $35.00
2025-06-11 19:01:00,457 - test_data_feed - INFO - test_data_feed:95 - MSFT: Retrieved 10 records, Latest close: $13.00
2025-06-11 19:01:00,531 - test_data_feed - INFO - test_data_feed:95 - GOOGL: Retrieved 10 records, Latest close: $48.00
2025-06-11 19:01:00,585 - test_data_feed - INFO - test_data_feed:101 - Market status: Closed
2025-06-11 19:01:00,586 - test_data_feed - INFO - test_data_feed:103 - DataFeed test completed successfully
2025-06-11 19:01:00,598 - main_test - WARNING - main:150 - Some API tests failed
2025-06-11 19:01:31,646 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:01:32,566 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:01:32,566 - test_alpaca - INFO - test_alpaca_connection:14 - Testing Alpaca API connection...
2025-06-11 19:01:32,782 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:32,831 - test_alpaca - INFO - test_alpaca_connection:22 - Account Status: Connected
2025-06-11 19:01:32,832 - test_alpaca - INFO - test_alpaca_connection:23 - Equity: $30,000.00
2025-06-11 19:01:32,832 - test_alpaca - INFO - test_alpaca_connection:24 - Cash: $30,000.00
2025-06-11 19:01:32,832 - test_alpaca - INFO - test_alpaca_connection:25 - Buying Power: $60,000.00
2025-06-11 19:01:32,879 - test_alpaca - INFO - test_alpaca_connection:32 - Market Open: False
2025-06-11 19:01:33,038 - test_alpaca - INFO - test_alpaca_connection:37 - Historical data retrieved: 1 records
2025-06-11 19:01:33,039 - test_alpaca - INFO - test_alpaca_connection:38 - Latest AAPL close: $770318.00
2025-06-11 19:01:33,041 - test_alpaca - INFO - test_alpaca_connection:42 - Alpaca API test completed successfully
2025-06-11 19:01:33,043 - test_fmp - INFO - test_fmp_connection:52 - Testing FMP API connection...
2025-06-11 19:01:33,272 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:01:33,338 - test_fmp - INFO - test_fmp_connection:60 - Company: Apple Inc.
2025-06-11 19:01:33,338 - test_fmp - INFO - test_fmp_connection:61 - Sector: Technology
2025-06-11 19:01:33,339 - test_fmp - INFO - test_fmp_connection:62 - Market Cap: $2,968,938,324,000
2025-06-11 19:01:33,464 - test_fmp - INFO - test_fmp_connection:69 - P/E Ratio: 37.***************
2025-06-11 19:01:33,464 - test_fmp - INFO - test_fmp_connection:70 - ROE: 1.****************
2025-06-11 19:01:33,465 - test_fmp - INFO - test_fmp_connection:74 - FMP API test completed successfully
2025-06-11 19:01:33,467 - test_data_feed - INFO - test_data_feed:84 - Testing DataFeed...
2025-06-11 19:01:33,613 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:01:33,924 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:01:33,925 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:01:34,135 - test_data_feed - INFO - test_data_feed:95 - AAPL: Retrieved 10 records, Latest close: $35.00
2025-06-11 19:01:34,195 - test_data_feed - INFO - test_data_feed:95 - MSFT: Retrieved 10 records, Latest close: $13.00
2025-06-11 19:01:34,262 - test_data_feed - INFO - test_data_feed:95 - GOOGL: Retrieved 10 records, Latest close: $48.00
2025-06-11 19:01:34,310 - test_data_feed - INFO - test_data_feed:101 - Market status: Closed
2025-06-11 19:01:34,311 - test_data_feed - INFO - test_data_feed:103 - DataFeed test completed successfully
2025-06-11 19:01:34,320 - main_test - INFO - main:147 - All API tests passed successfully
2025-06-11 19:02:27,995 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:29,159 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:29,159 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:29,169 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:29,260 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:30,192 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:30,192 - test_alpaca - INFO - test_alpaca_connection:14 - Testing Alpaca API connection...
2025-06-11 19:02:30,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:30,389 - test_alpaca - INFO - test_alpaca_connection:22 - Account Status: Connected
2025-06-11 19:02:30,390 - test_alpaca - INFO - test_alpaca_connection:23 - Equity: $30,000.00
2025-06-11 19:02:30,390 - test_alpaca - INFO - test_alpaca_connection:24 - Cash: $30,000.00
2025-06-11 19:02:30,390 - test_alpaca - INFO - test_alpaca_connection:25 - Buying Power: $60,000.00
2025-06-11 19:02:30,437 - test_alpaca - INFO - test_alpaca_connection:32 - Market Open: False
2025-06-11 19:02:30,594 - test_alpaca - INFO - test_alpaca_connection:37 - Historical data retrieved: 1 records
2025-06-11 19:02:30,594 - test_alpaca - INFO - test_alpaca_connection:38 - Latest AAPL close: $770318.00
2025-06-11 19:02:30,594 - test_alpaca - INFO - test_alpaca_connection:42 - Alpaca API test completed successfully
2025-06-11 19:02:30,595 - test_fmp - INFO - test_fmp_connection:52 - Testing FMP API connection...
2025-06-11 19:02:30,887 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:02:30,952 - test_fmp - INFO - test_fmp_connection:60 - Company: Apple Inc.
2025-06-11 19:02:30,952 - test_fmp - INFO - test_fmp_connection:61 - Sector: Technology
2025-06-11 19:02:30,952 - test_fmp - INFO - test_fmp_connection:62 - Market Cap: $2,968,938,324,000
2025-06-11 19:02:31,078 - test_fmp - INFO - test_fmp_connection:69 - P/E Ratio: 37.***************
2025-06-11 19:02:31,079 - test_fmp - INFO - test_fmp_connection:70 - ROE: 1.****************
2025-06-11 19:02:31,079 - test_fmp - INFO - test_fmp_connection:74 - FMP API test completed successfully
2025-06-11 19:02:31,079 - test_data_feed - INFO - test_data_feed:84 - Testing DataFeed...
2025-06-11 19:02:31,273 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:31,497 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:02:31,497 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:31,655 - test_data_feed - INFO - test_data_feed:95 - AAPL: Retrieved 10 records, Latest close: $35.00
2025-06-11 19:02:31,712 - test_data_feed - INFO - test_data_feed:95 - MSFT: Retrieved 10 records, Latest close: $13.00
2025-06-11 19:02:31,782 - test_data_feed - INFO - test_data_feed:95 - GOOGL: Retrieved 10 records, Latest close: $48.00
2025-06-11 19:02:31,833 - test_data_feed - INFO - test_data_feed:101 - Market status: Closed
2025-06-11 19:02:31,833 - test_data_feed - INFO - test_data_feed:103 - DataFeed test completed successfully
2025-06-11 19:02:52,154 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:53,208 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:53,209 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:02:53,213 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:53,308 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:54,216 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:02:54,217 - test_alpaca - INFO - test_alpaca_connection:14 - Testing Alpaca API connection...
2025-06-11 19:02:54,367 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:54,417 - test_alpaca - INFO - test_alpaca_connection:22 - Account Status: Connected
2025-06-11 19:02:54,417 - test_alpaca - INFO - test_alpaca_connection:23 - Equity: $30,000.00
2025-06-11 19:02:54,417 - test_alpaca - INFO - test_alpaca_connection:24 - Cash: $30,000.00
2025-06-11 19:02:54,418 - test_alpaca - INFO - test_alpaca_connection:25 - Buying Power: $60,000.00
2025-06-11 19:02:54,463 - test_alpaca - INFO - test_alpaca_connection:32 - Market Open: False
2025-06-11 19:02:54,628 - test_alpaca - INFO - test_alpaca_connection:37 - Historical data retrieved: 1 records
2025-06-11 19:02:54,628 - test_alpaca - INFO - test_alpaca_connection:38 - Latest AAPL close: $770318.00
2025-06-11 19:02:54,628 - test_alpaca - INFO - test_alpaca_connection:42 - Alpaca API test completed successfully
2025-06-11 19:02:54,629 - test_fmp - INFO - test_fmp_connection:52 - Testing FMP API connection...
2025-06-11 19:02:54,857 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:02:54,919 - test_fmp - INFO - test_fmp_connection:60 - Company: Apple Inc.
2025-06-11 19:02:54,920 - test_fmp - INFO - test_fmp_connection:61 - Sector: Technology
2025-06-11 19:02:54,920 - test_fmp - INFO - test_fmp_connection:62 - Market Cap: $2,968,938,324,000
2025-06-11 19:02:55,042 - test_fmp - INFO - test_fmp_connection:69 - P/E Ratio: 37.***************
2025-06-11 19:02:55,042 - test_fmp - INFO - test_fmp_connection:70 - ROE: 1.****************
2025-06-11 19:02:55,042 - test_fmp - INFO - test_fmp_connection:74 - FMP API test completed successfully
2025-06-11 19:02:55,042 - test_data_feed - INFO - test_data_feed:84 - Testing DataFeed...
2025-06-11 19:02:55,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:02:57,587 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:02:57,587 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:02:57,742 - test_data_feed - INFO - test_data_feed:95 - AAPL: Retrieved 10 records, Latest close: $35.00
2025-06-11 19:02:57,797 - test_data_feed - INFO - test_data_feed:95 - MSFT: Retrieved 10 records, Latest close: $13.00
2025-06-11 19:02:57,852 - test_data_feed - INFO - test_data_feed:95 - GOOGL: Retrieved 10 records, Latest close: $48.00
2025-06-11 19:02:57,898 - test_data_feed - INFO - test_data_feed:101 - Market status: Closed
2025-06-11 19:02:57,898 - test_data_feed - INFO - test_data_feed:103 - DataFeed test completed successfully
2025-06-11 19:02:57,899 - main_test - INFO - main:147 - All API tests passed successfully
2025-06-11 19:03:03,438 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:03:04,480 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,481 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:03:04,484 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:03:04,485 - main - INFO - run_live_trading:18 - Starting AI AutoTrader in LIVE mode...
2025-06-11 19:03:04,632 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:03:04,866 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:03:04,868 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:03:04,917 - main - WARNING - run_live_trading:25 - Market is currently closed. Consider using backtest mode.
2025-06-11 19:06:10,471 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:06:18,384 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:18,384 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-11 19:06:18,400 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:06:18,402 - main - INFO - run_gui:102 - Starting AI AutoTrader in GUI mode...
2025-06-11 19:06:18,997 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,288 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:06:19,432 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:19,655 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:06:19,656 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-11 19:06:52,973 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:06:53,872 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-11 19:06:54,334 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,565 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:06:54,714 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-11 19:06:54,955 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-11 19:06:54,955 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 03:54:17,767 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 03:54:18,836 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 03:54:19,304 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:19,588 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 03:54:19,781 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 03:54:20,005 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 03:54:20,007 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:41,703 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 06:34:42,673 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 06:34:42,674 - test_alpaca - INFO - test_alpaca_connection:14 - Testing Alpaca API connection...
2025-06-12 06:34:42,893 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:42,948 - test_alpaca - INFO - test_alpaca_connection:22 - Account Status: Connected
2025-06-12 06:34:42,950 - test_alpaca - INFO - test_alpaca_connection:23 - Equity: $30,000.00
2025-06-12 06:34:42,950 - test_alpaca - INFO - test_alpaca_connection:24 - Cash: $30,000.00
2025-06-12 06:34:42,951 - test_alpaca - INFO - test_alpaca_connection:25 - Buying Power: $60,000.00
2025-06-12 06:34:43,002 - test_alpaca - INFO - test_alpaca_connection:32 - Market Open: False
2025-06-12 06:34:43,248 - api.alpaca_client - WARNING - get_historical_data:172 - No data returned for AAPL
2025-06-12 06:34:43,250 - test_alpaca - WARNING - test_alpaca_connection:40 - No historical data retrieved
2025-06-12 06:34:43,250 - test_alpaca - INFO - test_alpaca_connection:42 - Alpaca API test completed successfully
2025-06-12 06:34:43,252 - test_fmp - INFO - test_fmp_connection:52 - Testing FMP API connection...
2025-06-12 06:34:43,549 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 06:34:43,618 - test_fmp - INFO - test_fmp_connection:60 - Company: Apple Inc.
2025-06-12 06:34:43,620 - test_fmp - INFO - test_fmp_connection:61 - Sector: Technology
2025-06-12 06:34:43,622 - test_fmp - INFO - test_fmp_connection:62 - Market Cap: $2,968,938,324,000
2025-06-12 06:34:43,761 - test_fmp - INFO - test_fmp_connection:69 - P/E Ratio: 37.***************
2025-06-12 06:34:43,763 - test_fmp - INFO - test_fmp_connection:70 - ROE: 1.****************
2025-06-12 06:34:43,764 - test_fmp - INFO - test_fmp_connection:74 - FMP API test completed successfully
2025-06-12 06:34:43,766 - test_data_feed - INFO - test_data_feed:84 - Testing DataFeed...
2025-06-12 06:34:43,976 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 06:34:44,227 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 06:34:44,228 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 06:34:44,443 - test_data_feed - INFO - test_data_feed:95 - AAPL: Retrieved 10 records, Latest close: $154.00
2025-06-12 06:34:44,499 - test_data_feed - INFO - test_data_feed:95 - MSFT: Retrieved 10 records, Latest close: $2.00
2025-06-12 06:34:44,564 - test_data_feed - INFO - test_data_feed:95 - GOOGL: Retrieved 10 records, Latest close: $61.00
2025-06-12 06:34:44,614 - test_data_feed - INFO - test_data_feed:101 - Market status: Closed
2025-06-12 06:34:44,615 - test_data_feed - INFO - test_data_feed:103 - DataFeed test completed successfully
2025-06-12 06:34:44,617 - main_test - INFO - main:147 - All API tests passed successfully
2025-06-12 08:26:02,401 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 08:26:10,464 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 08:26:11,061 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,302 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:11,486 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:11,723 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:11,724 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:17,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:17,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 08:26:17,875 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:18,124 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:18,124 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:18,497 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:18,738 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:18,739 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,043 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,286 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:19,288 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:19,609 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:19,855 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:19,855 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:26:20,155 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:26:20,386 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:26:20,386 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:20,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:20,974 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:27:20,976 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:21,311 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:21,535 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:27:21,535 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:21,842 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,067 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:27:22,067 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,380 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:22,602 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:27:22,604 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:27:22,919 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:27:23,146 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:27:23,148 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:23,476 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:23,719 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:28:23,721 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,053 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,304 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:28:24,306 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:24,626 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:24,854 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:28:24,856 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:25,181 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:25,512 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:28:25,513 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:28:25,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:28:26,079 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:28:26,081 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:26,408 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:26,643 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:29:26,645 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:26,971 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:27,222 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:29:27,224 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:27,552 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:27,784 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:29:27,785 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:28,104 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:28,338 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:29:28,340 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:29:28,652 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:29:29,012 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:29:29,014 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:29,344 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:29,643 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:30:29,646 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,065 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,307 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:30:30,310 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:30,633 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:30,889 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:30:30,891 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:31,283 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:31,513 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:30:31,515 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:30:31,866 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:30:32,127 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:30:32,128 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:32,537 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:32,818 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:31:32,819 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,150 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,402 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:31:33,403 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:33,715 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:33,948 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:31:33,950 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:34,270 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:34,525 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:31:34,527 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:31:34,833 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:31:35,063 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:31:35,063 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 08:34:51,003 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 08:34:57,881 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 08:34:58,553 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:58,786 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:34:58,944 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 08:34:59,186 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 08:34:59,189 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:12,678 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:12,678 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 09:13:12,824 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:13,102 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:13:13,103 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,219 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,440 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:13:14,448 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:14,747 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:14,966 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:13:14,966 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:15,276 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:15,877 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:13:15,877 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:13:16,177 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:13:16,395 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:13:16,395 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:17,972 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:18,254 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:14:18,255 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:18,573 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:18,802 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:14:18,803 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:19,103 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:19,607 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:14:19,609 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:19,932 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:20,200 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:14:20,202 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 09:14:20,516 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 09:14:20,814 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 09:14:20,816 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:36,017 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 10:04:36,191 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:36,479 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:36,480 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:36,813 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:37,057 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:37,059 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:37,376 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:37,615 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:37,617 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:37,929 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:38,162 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:38,163 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:38,472 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:38,700 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:38,701 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:04:39,012 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:04:39,265 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:04:39,267 - data_feed - INFO - __init__:26 - DataFeed initialized successfully
2025-06-12 10:07:08,918 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 10:07:09,078 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:09,359 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:09,360 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:09,522 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AAPL
2025-06-12 10:07:09,676 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:09,924 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:09,925 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:10,087 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AAPL
2025-06-12 10:07:10,245 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:10,484 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:10,485 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:10,648 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for MSFT
2025-06-12 10:07:10,804 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:11,026 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:11,027 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:11,188 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for SPY
2025-06-12 10:07:11,343 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:11,586 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:11,587 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:11,758 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GLD
2025-06-12 10:07:11,910 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:07:12,134 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:07:12,134 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:07:12,292 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for SPY
2025-06-12 10:11:12,368 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 10:11:12,563 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:12,857 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:12,858 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:13,031 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AAPL
2025-06-12 10:11:13,176 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:13,407 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:13,408 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:13,566 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for MSFT
2025-06-12 10:11:13,715 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:13,956 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:13,956 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:14,114 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GOOGL
2025-06-12 10:11:14,259 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:14,488 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:14,489 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:14,655 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TSLA
2025-06-12 10:11:14,806 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:15,029 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:15,030 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:15,193 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for SPY
2025-06-12 10:11:15,340 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:15,571 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:15,572 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:15,734 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for QQQ
2025-06-12 10:11:15,887 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:16,108 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:16,109 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:16,268 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for IWM
2025-06-12 10:11:16,410 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:16,655 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:16,655 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:16,807 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GLD
2025-06-12 10:11:16,948 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:17,175 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:17,175 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:17,336 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TLT
2025-06-12 10:11:17,486 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:17,731 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:17,732 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:17,891 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for XLF
2025-06-12 10:11:17,893 - screener - INFO - scan_market:20 - Scanning 10 symbols for TTM Squeeze signals...
2025-06-12 10:11:18,037 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:18,285 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:18,286 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:18,445 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AAPL
2025-06-12 10:11:18,445 - screener - WARNING - scan_market:28 - Insufficient data for AAPL
2025-06-12 10:11:18,589 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:18,883 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:18,884 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:19,048 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for MSFT
2025-06-12 10:11:19,050 - screener - WARNING - scan_market:28 - Insufficient data for MSFT
2025-06-12 10:11:19,199 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:19,425 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:19,425 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:19,581 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TSLA
2025-06-12 10:11:19,582 - screener - WARNING - scan_market:28 - Insufficient data for TSLA
2025-06-12 10:11:19,725 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:19,957 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:19,957 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:20,115 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AMZN
2025-06-12 10:11:20,116 - screener - WARNING - scan_market:28 - Insufficient data for AMZN
2025-06-12 10:11:20,261 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:20,495 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:20,496 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:20,648 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GOOGL
2025-06-12 10:11:20,649 - screener - WARNING - scan_market:28 - Insufficient data for GOOGL
2025-06-12 10:11:20,795 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:21,018 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:21,020 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:21,213 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for SPY
2025-06-12 10:11:21,214 - screener - WARNING - scan_market:28 - Insufficient data for SPY
2025-06-12 10:11:21,357 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:21,601 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:21,602 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:21,762 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for QQQ
2025-06-12 10:11:21,763 - screener - WARNING - scan_market:28 - Insufficient data for QQQ
2025-06-12 10:11:21,904 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:22,135 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:22,137 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:22,296 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GLD
2025-06-12 10:11:22,297 - screener - WARNING - scan_market:28 - Insufficient data for GLD
2025-06-12 10:11:22,441 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:22,681 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:22,682 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:22,919 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TLT
2025-06-12 10:11:22,921 - screener - WARNING - scan_market:28 - Insufficient data for TLT
2025-06-12 10:11:23,070 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:11:23,306 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:11:23,307 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:11:23,462 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for IWM
2025-06-12 10:11:23,464 - screener - WARNING - scan_market:28 - Insufficient data for IWM
2025-06-12 10:11:23,464 - screener - INFO - scan_market:41 - Scan complete. Found 0 signals.
2025-06-12 10:12:20,923 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 10:12:21,902 - root - INFO - setup_logging:97 - Logging system initialized successfully
2025-06-12 10:12:22,306 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:22,603 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:22,751 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:22,998 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:23,000 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:25,945 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:25,945 - trade_executor - INFO - __init__:21 - TradeExecutor initialized successfully
2025-06-12 10:12:25,954 - screener - INFO - scan_market:20 - Scanning 10 symbols for TTM Squeeze signals...
2025-06-12 10:12:26,094 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:26,314 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:26,315 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:26,479 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AAPL
2025-06-12 10:12:26,480 - screener - WARNING - scan_market:28 - Insufficient data for AAPL
2025-06-12 10:12:26,624 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:26,863 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:26,863 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:27,030 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for MSFT
2025-06-12 10:12:27,031 - screener - WARNING - scan_market:28 - Insufficient data for MSFT
2025-06-12 10:12:27,177 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:27,425 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:27,426 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:27,586 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TSLA
2025-06-12 10:12:27,587 - screener - WARNING - scan_market:28 - Insufficient data for TSLA
2025-06-12 10:12:27,732 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:27,960 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:27,961 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:28,118 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for AMZN
2025-06-12 10:12:28,118 - screener - WARNING - scan_market:28 - Insufficient data for AMZN
2025-06-12 10:12:28,262 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:28,485 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:28,485 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:28,637 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GOOGL
2025-06-12 10:12:28,637 - screener - WARNING - scan_market:28 - Insufficient data for GOOGL
2025-06-12 10:12:28,783 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:29,005 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:29,006 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:29,163 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for SPY
2025-06-12 10:12:29,164 - screener - WARNING - scan_market:28 - Insufficient data for SPY
2025-06-12 10:12:29,308 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:29,549 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:29,549 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:29,707 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for QQQ
2025-06-12 10:12:29,708 - screener - WARNING - scan_market:28 - Insufficient data for QQQ
2025-06-12 10:12:29,853 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:30,072 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:30,074 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:30,240 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for GLD
2025-06-12 10:12:30,241 - screener - WARNING - scan_market:28 - Insufficient data for GLD
2025-06-12 10:12:30,399 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:30,635 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:30,637 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:30,801 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for TLT
2025-06-12 10:12:30,802 - screener - WARNING - scan_market:28 - Insufficient data for TLT
2025-06-12 10:12:30,956 - api.alpaca_client - INFO - __init__:30 - Connected to Alpaca API. Account status: ACTIVE
2025-06-12 10:12:31,182 - api.fmp_client - INFO - __init__:27 - Connected to Financial Modeling Prep API
2025-06-12 10:12:31,183 - data_feed - INFO - __init__:25 - DataFeed initialized successfully
2025-06-12 10:12:31,340 - data_feed - INFO - get_stock_data:42 - Retrieved 8 bars from Alpaca for IWM
2025-06-12 10:12:31,341 - screener - WARNING - scan_market:28 - Insufficient data for IWM
2025-06-12 10:12:31,342 - screener - INFO - scan_market:41 - Scan complete. Found 0 signals.
